<?php

namespace BankingModule\TideModule\Authenticators;

use BankingModule\TideModule\Exceptions\UnsuccessfulAuthenticationException;
use BankingModule\TideModule\Responses\AuthenticationResponse;
use GuzzleHttp\Client as GuzzleClient;

class Authenticator
{
    /**
     * @var GuzzleClient
     */
    private $guzzleClient;

    /**
     * @var string
     */
    private $uri;

    /**
     * @var array
     */
    private $apiId;

    /**
     * @var string
     */
    private $apiKey;

    /**
     * @param GuzzleClient $client
     * @param string $uri
     * @param string $apiId
     * @param string $apiKey
     */
    public function __construct(GuzzleClient $client, $uri, $apiId, $apiKey)
    {
        $this->guzzleClient = $client;
        $this->uri = $uri;
        $this->apiId = $apiId;
        $this->apiKey = $apiKey;
    }

    /**
     * @return AuthenticationResponse
     */
    public function authenticate(): AuthenticationResponse
    {
        $response = $this->getResponse($this->apiId, $this->apiKey);

        if ($response->isFailed()) {
            throw UnsuccessfulAuthenticationException::general();
        }

        return $response;
    }

    /**
     * @param string $apiId
     * @param string $apiKey
     * @return mixed
     */
    private function getResponse($apiId, $apiKey)
    {
        $response = $this
            ->guzzleClient
            ->post(
                $this->uri,
                [
                    'json' => [
                        "apiId" => $apiId,
                        "apiKey" => $apiKey
                    ]
                ]
            );

        $data = json_decode($response->getBody()->__toString(), true);

        return AuthenticationResponse::fromArray($data);
    }

}
