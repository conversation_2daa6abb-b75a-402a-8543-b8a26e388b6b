<?php

namespace RenewalModule\Views;

use DateTime;
use Entities\Company;
use Entities\Customer;
use Entities\Payment\Token;

class AutoRenewalEmailView
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var ServiceView[]
     */
    private $services = [];

    /**
     * @var array
     */
    private $benefits = [];

    /**
     * @var array
     */
    private $consequences = [];

    /**
     * @var string
     */
    private $ordersLink;

    /**
     * @var DateTime
     */
    private $earliestServiceExpiryDate;

    /**
     * @var int
     */
    private $lowestServiceDaysToExpire;

    /**
     * @var string
     */
    private $paymentMethodUpdateLink;

    /**
     * @var string
     */
    private $myServicesLink;

    /**
     * @var string
     */
    private $howToChangeRegisteredAddressLink;

    /**
     * @param Customer $customer
     * @param Company $company
     * @param ServiceView[] $services
     * @param array $benefits
     * @param array $consequences
     * @param string $ordersLink
     * @param string $paymentMethodUpdateLink
     * @param string $myServicesLink
     * @param DateTime $earliestServiceExpiryDate
     * @param int $lowestServiceDaysToExpire
     * @param string $howToChangeRegisteredAddressLink
     */
    public function __construct(
        Customer $customer,
        Company $company,
        array $services,
        array $benefits,
        array $consequences,
        string $ordersLink,
        string $paymentMethodUpdateLink,
        string $myServicesLink,
        DateTime $earliestServiceExpiryDate,
        int $lowestServiceDaysToExpire,
        string $howToChangeRegisteredAddressLink
    )
    {
        $this->customer = $customer;
        $this->company = $company;
        $this->services = $services;
        $this->benefits = $benefits;
        $this->consequences = $consequences;
        $this->ordersLink = $ordersLink;
        $this->earliestServiceExpiryDate = $earliestServiceExpiryDate;
        $this->lowestServiceDaysToExpire = $lowestServiceDaysToExpire;
        $this->paymentMethodUpdateLink = $paymentMethodUpdateLink;
        $this->myServicesLink = $myServicesLink;
        $this->howToChangeRegisteredAddressLink = $howToChangeRegisteredAddressLink;
    }

    /**
     * @return Customer
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    /**
     * @return Company
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * @return ServiceView[]
     */
    public function getServices()
    {
        return $this->services;
    }

    /**
     * @return array
     */
    public function getBenefits()
    {
        return $this->benefits;
    }

    /**
     * @return array
     */
    public function getConsequences()
    {
        return $this->consequences;
    }

    /**
     * @return string
     */
    public function getOrdersLink()
    {
        return $this->ordersLink;
    }

    /**
     * @return string
     */
    public function getPaymentMethodUpdateLink()
    {
        return $this->paymentMethodUpdateLink;
    }

    /**
     * @return string
     */
    public function getMyServicesLink()
    {
        return $this->myServicesLink;
    }

    /**
     * @return DateTime
     */
    public function getEarliestServiceExpiryDate()
    {
        return $this->earliestServiceExpiryDate;
    }

    /**
     * @return int
     */
    public function getLowestServiceDaysToExpire()
    {
        return $this->lowestServiceDaysToExpire;
    }

    /**
     * @return string
     */
    public function getServiceWord()
    {
        return (count($this->getServices()) == 1) ? 'service' : 'services';
    }

    /**
     * @return string
     */
    public function getServiceVerb()
    {
        return (count($this->getServices()) == 1) ? 'has' : 'have';
    }

    /**
     * @return bool
     */
    public function hasToShowRenewLink()
    {
        return count($this->getServices()) == 1;
    }

    /**
     * @return Token|NULL
     */
    public function getToken()
    {
        return $this->getCustomer()->getActiveToken();
    }

    /**
     * @return bool
     */
    public function hasBenefits()
    {
        return !empty($this->getBenefits());
    }

    /**
     * @return bool
     */
    public function hasConsequences()
    {
        return !empty($this->getConsequences());
    }

    public function hasToShowAdditionalAddressBlock()
    {
        foreach ($this->services as $service) {
            if ($service->isAddressType()) {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * @return string
     */
    public function getHowToChangeRegisteredAddressLink(): string
    {
        return $this->howToChangeRegisteredAddressLink;
    }

}