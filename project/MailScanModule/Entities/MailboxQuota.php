<?php

declare(strict_types=1);

namespace MailScanModule\Entities;

use DateTime;
use Gedmo\Mapping\Annotation as Gedmo;
use MailScanModule\Enums\QuotaTypeEnum;

/**
 * @Orm\Entity(repositoryClass = "MailScanModule\Repositories\MailboxQuotasRepository")
 * @Gedmo\Loggable(logEntryClass="LoggableModule\Entities\LogEntry")
 * @Orm\Table(name="cms2_mailbox_quotas")
 */
class MailboxQuotas implements \JsonSerializable
{
    /**
     * @Orm\Column(type="integer", name="mailboxQuotasId")
     * @Orm\Id
     * @Orm\GeneratedValue(strategy="AUTO")
     */
    private int $mailboxQuotasId; /** @phpstan-ignore-line */

    /**
     * @Orm\Column(type="integer", name="companyId")
     */
    private int $companyId;

    /**
     * @Orm\Column(type="integer", name="serviceId")
     */
    private int $serviceId;

    /**
     * @Orm\Column(type="string", name="quotaType", enumType="MailScanModule\Enums\QuotaTypeEnum")
     */
    private QuotaTypeEnum $quotaType;

    /**
     * @Orm\Column(type="integer", name="usageCount")
     */
    private int $usageCount;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     */
    private DateTime $dtExpires;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private DateTime $dtc; /** @phpstan-ignore-line */

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create", on="update")
     */
    private DateTime $dtm; /** @phpstan-ignore-line */

    public function __construct()
    {
        $this->usageCount = 0;
    }

    public function getMailboxQuotasId(): int
    {
        return $this->mailboxQuotasId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getServiceId(): int
    {
        return $this->serviceId;
    }

    public function getQuotaType(): QuotaTypeEnum
    {
        return $this->quotaType;
    }

    public function getUsageCount(): int
    {
        return $this->usageCount;
    }

    public function getDtExpires(): DateTime
    {
        return $this->dtExpires;
    }

    public function getDtc(): DateTime
    {
        return $this->dtc;
    }

    public function getDtm(): DateTime
    {
        return $this->dtm;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function setServiceId(int $serviceId): self
    {
        $this->serviceId = $serviceId;
        return $this;
    }

    public function setQuotaType(QuotaTypeEnum $quotaType): self
    {
        $this->quotaType = $quotaType;
        return $this;
    }

    public function setUsageCount(int $usageCount): self
    {
        $this->usageCount = $usageCount;
        return $this;
    }

    public function setDtExpires(DateTime $dtExpires): self
    {
        $this->dtExpires = $dtExpires;
        return $this;
    }


    public function jsonSerialize(): array
    {
        return [
            'mailboxQuotasId' => $this->mailboxQuotasId,
            'companyId' => $this->companyId,
            'serviceId' => $this->serviceId,
            'quotaType' => $this->quotaType->value,
            'usageCount' => $this->usageCount,
            'dtExpires' => $this->dtExpires,
            'dtc' => $this->dtc,
            'dtm' => $this->dtm,
        ];
    }
}