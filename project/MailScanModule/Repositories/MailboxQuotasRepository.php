<?php

declare(strict_types=1);

namespace MailScanModule\Repositories;

use Entities\Service;
use MailScanModule\Entities\MailboxQuotas;
use MailScanModule\Enums\QuotaTypeEnum;
use OrmModule\Repositories\DoctrineRepository_Abstract;

class MailboxQuotasRepository extends DoctrineRepository_Abstract
{
    public function getMailboxQuotasByServiceId(?int $serviceId): ?MailboxQuotas
    {
        if ($serviceId === null) {
            return null;
        }

        return $this->findOneBy(['serviceId' => $serviceId]);
    }

    /**
     * @throws \Exception
     */
    public function getLatestActiveMailboxQuotasByServiceIdAndType(Service $service, QuotaTypeEnum $quotaType): ?MailboxQuotas
    {
        return $this->createQueryBuilder('mq')
            ->where('mq.serviceId = :serviceId')
            ->andWhere('mq.quotaType = :quotaType')
            ->andWhere('mq.dtExpires >= CURRENT_DATE()')
            ->setParameter('serviceId', $service->getServiceId())
            ->setParameter('quotaType', $quotaType->value)
            ->orderBy('mq.dtExpires', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return MailboxQuotas[]
     * @throws \Exception
     */
    public function getActiveMailboxQuotasByServiceId(int $serviceId): array
    {
        return $this->createQueryBuilder('mq')
            ->where('mq.serviceId = :serviceId')
            ->andWhere('mq.dtExpires >= CURRENT_DATE()')
            ->setParameter('serviceId', $serviceId)
            ->orderBy('mq.dtExpires', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @throws \Exception
     */
    public function persistQuota(MailboxQuotas $entity): void
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
    }

    /**
     * @throws \Exception
     */
    public function findOrCreateQuota(Service $service, int $companyId, QuotaTypeEnum $quotaType): MailboxQuotas
    {
        $quota = $this->getLatestActiveMailboxQuotasByServiceIdAndType($service, $quotaType);

        if (!empty($quota)) {
            return $quota;
        }

        $quota = new MailboxQuotas();
        $quota->setServiceId($service->getServiceId())
            ->setCompanyId($companyId)
            ->setQuotaType($quotaType)
            ->setUsageCount(0)
            ->setDtExpires($this->getNextDtExpires($service));

        $this->persistQuota($quota);

        return $quota;
    }

    private function getNextDtExpires(Service $service): \DateTime
    {
        $quotaDtExpires = $service->getDtStart();
        for ($i = 0; $i < 12; $i++) {
            $quotaDtExpires->modify('+1 month');
            if ($quotaDtExpires > new \DateTime()) {
                break;
            }
        }
        return $quotaDtExpires;
    }
}