<?php

namespace CsmsModule\Views;

use CsmsModule\Dto\CsmsUkDirectorData;

class CsmsUkDirectorView
{
    /**
     * @var CsmsUkDirectorData
     */
    private $csmsUkDirectorData;

    /**
     * @var CsmsReportView|null
     */
    private $csmsReportView;

    /**
     * @var bool
     */
    private $canSeeCredits;

    /**
     * @var int
     */
    private $creditsLeft;

    public function __construct(
        CsmsUkDirectorData $csmsUkDirectorData,
        ?CsmsReportView $csmsReportView,
        bool $canSeeCredits = false,
        int $creditsLeft = 0
    ) {
        $this->csmsUkDirectorData = $csmsUkDirectorData;
        $this->csmsReportView = $csmsReportView;
        $this->canSeeCredits = $canSeeCredits;
        $this->creditsLeft = $creditsLeft;
    }

    public function getCsmsUkDirectorData(): CsmsUkDirectorData
    {
        return $this->csmsUkDirectorData;
    }

    public function getCsmsReportView(): ?CsmsReportView
    {
        return $this->csmsReportView;
    }

    public function hasPurchasedReport(): bool
    {
        return !is_null($this->getCsmsReportView());
    }

    public function canSeeCredits(): bool
    {
        return $this->canSeeCredits;
    }

    public function canUseCredits(): bool
    {
        return $this->getCreditsLeft() > 0;
    }

    public function getCreditsLeft(): int
    {
        return $this->creditsLeft;
    }
}
