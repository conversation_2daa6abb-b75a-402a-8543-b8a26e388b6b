<?php

declare(strict_types=1);

namespace LoggableModule\Services;

use http\Exception\InvalidArgumentException;
use LoggableModule\Dto\PropertyEntity;
use LoggableModule\Entities\LogEntry;
use LoggableModule\Repositories\LoggableRepository;
use Services\UserService;

readonly class LoggableService
{
    const CRON_JOB_DB_IDENTIFIER = 'cron';
    const CRON_JOB_ALIAS_NAME = 'System';
    const CUSTOMER_ALIAS_NAME = 'Customer';

    public function __construct(
        private UserService $userService,
        private LoggableRepository $loggableRepository
    ) {
    }


    private function getLogAuthor(LogEntry $logEntry): string
    {
        if ($logEntry->getUsername() === self::CRON_JOB_DB_IDENTIFIER) {
            return self::CRON_JOB_ALIAS_NAME;
        }

        if (!empty($logEntry->getUserId())) {

            $user = $this->userService->getUserById($logEntry->getUserId());
            if (!!empty($user)) {
                throw new InvalidArgumentException('Admin user not found');
            }

            return sprintf('%s %s', $user->getFirstName(), $user->getLastName());
        }

        if ($logEntry->getCustomerId()) {
            return self::CUSTOMER_ALIAS_NAME;
        }

        throw new InvalidArgumentException('LogEntry has no author');
    }

    private function searchForPropertyChange(array $logEntries, string $property): ?LogEntry
    {
        foreach ($logEntries as $logEntry) {
            if (in_array($property, array_keys($logEntry->getData()))) {
                return $logEntry;
            }
        }

        return null;
    }

    public function getLastPropertyChangeFromObject(string $className, int $objectId, string $property): ?PropertyEntity
    {
        $latestLogEntries = $this->loggableRepository->getChangesFromObject($className, $objectId);
        if (!$latestLogEntries) {
            return null;
        }

        $latestLogEntry = $this->searchForPropertyChange($latestLogEntries, $property);
        if (empty($latestLogEntry)) {
            return null;
        }

        return new PropertyEntity(
            $latestLogEntry->getId(),
            $this->getLogAuthor($latestLogEntry),
            $latestLogEntry->getLoggedAt(),
            $latestLogEntry->getVersion(),
            $latestLogEntry->getAction(),
            $latestLogEntry->getData()
        );
    }

    public function getLatestPropertyChangeString(string $className, int $objectId, string $property): ?string
    {
        $latestChange = $this->getLastPropertyChangeFromObject($className, $objectId, $property);
        if (!$latestChange) {
            return null;
        }

        if (empty($latestChange->data[$property])) {
            return null;
        }

        return sprintf(
            '%s at %s by %s',
            $latestChange->action,
            ($latestChange->loggedAt)->format('d-m-Y H:i:s'),
            $latestChange->logAuthor
        );
    }
}