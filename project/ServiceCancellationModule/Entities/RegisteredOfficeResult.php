<?php

namespace ServiceCancellationModule\Entities;

use CompaniesHouse\Entities\CompanyDetails\Address;

class RegisteredOfficeResult extends Result
{
    /**
     * @var Address
     */
    private $address;
    
    public static function fromPassedAndAddress(bool $passed, Address $address): self
    {
        $self = new self;
        $self->setValid($passed);
        $self->setAddress($address);
        return $self;
    }
    
    public function getAddress(): Address
    {
        return $this->address;
    }

    public function getAddressString(): string
    {
        return $this->address->getAddressString();
    }

    public function setAddress(Address $address): void
    {
        $this->address = $address;
    }
}